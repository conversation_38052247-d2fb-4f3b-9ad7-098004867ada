stages:
  - test
  - build
  - deploy

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_DRIVER: overlay2
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA

test:
  stage: test
  image: golang:1.24-alpine3.21
  script:
    - go test ./...

build:
  stage: build
  image: docker:28
  services:
    - name: docker:28-dind
      alias: docker
      command: ["--insecure-registry=nexus:8081"]
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER $CI_REGISTRY --password-stdin
  script:
    - docker build -t $IMAGE_TAG .
    - docker push $IMAGE_TAG

deploy:
  stage: deploy
  image: docker:28
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER $CI_REGISTRY --password-stdin
  script:
    - docker rm -f go-rest1 || true
    - docker pull $IMAGE_TAG
    - docker run -d --name go-rest1 --network nginx-net $IMAGE_TAG
